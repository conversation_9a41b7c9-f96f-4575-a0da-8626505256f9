{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "target": "es2015", "downlevelIteration": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"~/*": ["./src/*"]}, "forceConsistentCasingInFileNames": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/app/(external-pages)/products/data/cloud-monitoring-faqs", "src/components/modals/CreateAdmin"], "exclude": ["node_modules"]}