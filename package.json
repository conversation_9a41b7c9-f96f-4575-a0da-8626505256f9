{"name": "telex", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "next dev", "build": "tsc && next build", "preview": "next preview", "tauri": "tauri"}, "dependencies": {"@tauri-apps/api": "^1", "next": "^15.4.5", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@tauri-apps/cli": "^1", "@types/node": "24.2.0", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "typescript": "~5.6.2", "vite": "^6.0.3"}}